#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web端修复效果
"""

import sqlite3

def check_database():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")
    
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 查看TEST001用户的任务
        cursor.execute('''
            SELECT id, tracking_number, creator_id, creator_name, task_stage, status, created_at
            FROM task_queue 
            WHERE creator_id = 'TEST001' OR creator_name = '测试用户'
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        tasks = cursor.fetchall()
        print(f"TEST001用户的任务 ({len(tasks)} 个):")
        
        for task in tasks:
            print(f"  ID: {task[0][:8]}...")
            print(f"  跟踪号: {task[1]}")
            print(f"  创建者ID: {task[2]}")
            print(f"  创建者名: {task[3]}")
            print(f"  阶段: {task[4]}")
            print(f"  状态: {task[5]}")
            print(f"  时间: {task[6]}")
            print()
        
        # 查看最近的所有任务
        print("最近的所有任务:")
        cursor.execute('''
            SELECT tracking_number, creator_id, creator_name, status, created_at
            FROM task_queue 
            ORDER BY created_at DESC
            LIMIT 10
        ''')
        
        all_tasks = cursor.fetchall()
        for task in all_tasks:
            print(f"  {task[0]} | {task[1]} | {task[2]} | {task[3]} | {task[4]}")
        
        conn.close()
        return len(tasks) > 0
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 检查Web端任务状态...")
    print("=" * 50)
    
    check_database()
    
    print("\n💡 如果看到TEST001用户的任务，说明数据存在")
    print("如果Web端看不到，说明查询逻辑有问题")

if __name__ == "__main__":
    main()
