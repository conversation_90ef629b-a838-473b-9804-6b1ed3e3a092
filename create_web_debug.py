#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建Web端调试工具
通过添加JavaScript代码到页面来调试Web端问题
"""

def create_web_debug_script():
    """创建Web端调试脚本"""
    debug_script = '''
<!-- Web端调试脚本 -->
<script>、
// 添加调试按钮到页面
function addDebugButton() {
    const debugBtn = document.createElement('button');
    debugBtn.innerHTML = '🔧 调试信息';
    debugBtn.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 10000;
        background: #ff4444;
        color: white;
        border: none;
        padding: 10px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 12px;
    `;
    
    debugBtn.onclick = function() {
        showDebugInfo();
    };
    
    document.body.appendChild(debugBtn);
}

// 显示调试信息
function showDebugInfo() {
    const debugInfo = {
        authToken: authToken ? authToken.substring(0, 20) + '...' : 'null',
        currentUser: currentUser,
        apiBaseUrl: API_BASE_URL,
        location: location.href,
        tableDataCount: tableData ? tableData.length : 0,
        latestTasksCount: window.__latestTasks ? window.__latestTasks.length : 0
    };
    
    console.log('🔧 Web端调试信息:', debugInfo);
    
    // 创建调试弹窗
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
    `;
    
    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 10px;
        max-width: 600px;
        max-height: 80%;
        overflow-y: auto;
    `;
    
    content.innerHTML = `
        <h3>🔧 Web端调试信息</h3>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto;">${JSON.stringify(debugInfo, null, 2)}</pre>
        <h4>最近任务数据 (前5个):</h4>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto;">${JSON.stringify((window.__latestTasks || []).slice(0, 5), null, 2)}</pre>
        <button onclick="testCreateTask()" style="margin: 10px 5px; padding: 8px 15px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">测试创建任务</button>
        <button onclick="testGetTasks()" style="margin: 10px 5px; padding: 8px 15px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">测试获取任务</button>
        <button onclick="modal.remove()" style="margin: 10px 5px; padding: 8px 15px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">关闭</button>
    `;
    
    modal.appendChild(content);
    document.body.appendChild(modal);
    
    modal.onclick = function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    };
}

// 测试创建任务
async function testCreateTask() {
    const testContainer = 'DEBUG' + Date.now();
    console.log('🧪 测试创建任务:', testContainer);
    
    try {
        const result = await apiCall('/tasks/quick-create', {
            method: 'POST',
            body: JSON.stringify({ container_number: testContainer }),
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        console.log('✅ 任务创建结果:', result);
        alert(`✅ 任务创建成功!\\nID: ${result.task_id}\\n号码: ${result.container_number}\\n用户: ${result.user_id}`);
        
        // 刷新任务列表
        await fetchTasksFromAPI();
        renderTable();
        
    } catch (error) {
        console.error('❌ 任务创建失败:', error);
        alert('❌ 任务创建失败: ' + error.message);
    }
}

// 测试获取任务
async function testGetTasks() {
    console.log('🧪 测试获取任务列表');
    
    try {
        const result = await apiCall('/tasks/my-tasks?limit=10');
        console.log('✅ 任务列表结果:', result);
        alert(`✅ 获取任务成功!\\n数量: ${result.length}\\n最新任务: ${result[0] ? result[0].container_number : '无'}`);
    } catch (error) {
        console.error('❌ 获取任务失败:', error);
        alert('❌ 获取任务失败: ' + error.message);
    }
}

// 页面加载完成后添加调试按钮
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addDebugButton);
} else {
    addDebugButton();
}
</script>
'''
    
    return debug_script

def main():
    """主函数"""
    print('🔧 Web端调试工具生成器')
    print('=' * 50)
    
    debug_script = create_web_debug_script()
    
    # 保存到文件
    with open('web_debug.html', 'w', encoding='utf-8') as f:
        f.write(debug_script)
    
    print('✅ 调试脚本已生成: web_debug.html')
    print()
    print('使用方法:')
    print('1. 打开 web_debug.html 复制其中的 <script> 内容')
    print('2. 在浏览器开发者工具的Console中粘贴执行')
    print('3. 页面右上角会出现 "🔧 调试信息" 按钮')
    print('4. 点击按钮查看Web端的详细状态')
    print('5. 可以直接在弹窗中测试API调用')
    print()
    print('或者直接在浏览器Console中执行以下代码:')
    print('-' * 50)
    print(debug_script.replace('<!-- Web端调试脚本 -->\n<script>', '').replace('</script>', ''))

if __name__ == "__main__":
    main()
