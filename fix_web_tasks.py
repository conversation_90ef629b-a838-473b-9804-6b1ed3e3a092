#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Web端任务显示问题
"""

import sqlite3
import json
from datetime import datetime

def check_current_situation():
    """检查当前情况"""
    print("🔍 检查当前任务和用户情况...")
    
    try:
        # 检查任务队列
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 查看最近的任务
        cursor.execute('''
            SELECT tracking_number, creator_id, creator_name, task_stage, status, created_at
            FROM task_queue 
            ORDER BY created_at DESC 
            LIMIT 10
        ''')
        
        tasks = cursor.fetchall()
        print(f"最近10个任务:")
        for task in tasks:
            print(f"  {task[0]} | 创建者ID: {task[1]} | 创建者: {task[2]} | 阶段: {task[3]} | 状态: {task[4]}")
        
        # 统计不同creator_id
        cursor.execute('''
            SELECT creator_id, COUNT(*) as count
            FROM task_queue 
            GROUP BY creator_id
            ORDER BY count DESC
        ''')
        
        creators = cursor.fetchall()
        print(f"\n按创建者统计:")
        for creator in creators:
            print(f"  创建者ID: {creator[0]} | 任务数: {creator[1]}")
        
        conn.close()
        
        # 检查Web API用户
        print(f"\n已知的Web用户ID:")
        web_users = ['001', '002', '003', 'ADMIN', 'TEST001']
        for user_id in web_users:
            print(f"  {user_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def create_test_task_for_web_user():
    """为Web用户创建测试任务"""
    print("\n🔧 为Web用户创建测试任务...")
    
    try:
        # 导入任务管理器
        import sys
        sys.path.append('.')
        from task_manager import TaskManager
        
        task_manager = TaskManager()
        
        # 为用户001创建一个测试任务
        test_tracking = "WEBTEST123456"
        task_id = task_manager.create_task(
            tracking_number=test_tracking,
            task_type="container",
            creator_id="001",  # 使用Web用户的ID
            creator_name="李乐",
            carrier="MSC",
            priority=1,
            remarks="Web端测试任务"
        )
        
        print(f"✅ 为用户001创建测试任务: {task_id}")
        
        # 再创建一个给TEST001用户
        test_tracking2 = "WEBTEST789012"
        task_id2 = task_manager.create_task(
            tracking_number=test_tracking2,
            task_type="bill_of_lading",
            creator_id="TEST001",  # 使用测试用户的ID
            creator_name="测试用户",
            carrier="COSCO",
            priority=0,
            remarks="Web端测试任务2"
        )
        
        print(f"✅ 为用户TEST001创建测试任务: {task_id2}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试任务失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_task_query():
    """测试用户任务查询"""
    print("\n🧪 测试用户任务查询...")
    
    try:
        import sys
        sys.path.append('.')
        from api.services.user_task_service import UserTaskService
        from api.models.user_schemas import UserInfo, UserRole, UserStatus
        from datetime import datetime
        
        # 创建测试用户信息
        test_user = UserInfo(
            user_id="001",
            name="李乐",
            role=UserRole.SENIOR,
            avatar="L",
            status=UserStatus.ACTIVE,
            permissions=["query:create", "query:view"],
            created_at=datetime.now(),
            last_login=datetime.now(),
            query_count=0,
            success_rate=0.0
        )
        
        # 创建用户任务服务
        service = UserTaskService()
        
        # 查询用户任务
        import asyncio
        tasks = asyncio.run(service.get_user_tasks(test_user, limit=10))
        
        print(f"用户 {test_user.user_id} 的任务数量: {len(tasks)}")
        for task in tasks:
            print(f"  任务ID: {task.task_id}")
            print(f"  跟踪号: {task.container_number}")
            print(f"  状态: {task.status}")
            print(f"  创建时间: {task.created_at}")
            print()
        
        return len(tasks) > 0
        
    except Exception as e:
        print(f"❌ 测试用户任务查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_existing_tasks():
    """修复现有任务的creator_id"""
    print("\n🔧 修复现有任务的creator_id...")
    
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 查找可能是Web用户创建但creator_id不正确的任务
        # 通常这些任务的creator_id可能是用户名而不是ID
        cursor.execute('''
            SELECT id, tracking_number, creator_id, creator_name, created_at
            FROM task_queue 
            WHERE creator_id NOT IN ('001', '002', '003', 'ADMIN', 'TEST001', 'system', 'api_user')
            AND datetime(created_at) > datetime('now', '-24 hours')
            ORDER BY created_at DESC
        ''')
        
        problematic_tasks = cursor.fetchall()
        
        if problematic_tasks:
            print(f"发现 {len(problematic_tasks)} 个可能有问题的任务:")
            for task in problematic_tasks:
                print(f"  ID: {task[0][:8]}... | 跟踪号: {task[1]} | 创建者ID: {task[2]} | 创建者名: {task[3]}")
            
            # 尝试修复：如果creator_name是"李乐"，则设置creator_id为"001"
            cursor.execute('''
                UPDATE task_queue 
                SET creator_id = '001'
                WHERE creator_name = '李乐' 
                AND creator_id != '001'
                AND datetime(created_at) > datetime('now', '-24 hours')
            ''')
            
            updated_count = cursor.rowcount
            
            # 修复测试用户的任务
            cursor.execute('''
                UPDATE task_queue 
                SET creator_id = 'TEST001'
                WHERE creator_name = '测试用户' 
                AND creator_id != 'TEST001'
                AND datetime(created_at) > datetime('now', '-24 hours')
            ''')
            
            updated_count += cursor.rowcount
            
            conn.commit()
            print(f"✅ 修复了 {updated_count} 个任务的creator_id")
        else:
            print("✅ 没有发现需要修复的任务")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复任务失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始修复Web端任务显示问题...")
    print("=" * 60)
    
    # 1. 检查当前情况
    if not check_current_situation():
        return
    
    # 2. 修复现有任务
    fix_existing_tasks()
    
    # 3. 创建测试任务
    create_test_task_for_web_user()
    
    # 4. 测试用户任务查询
    query_success = test_user_task_query()
    
    print("\n" + "=" * 60)
    print("📋 修复总结:")
    if query_success:
        print("✅ 用户任务查询正常，Web端应该能看到任务了")
    else:
        print("❌ 用户任务查询仍有问题，需要进一步调试")
    
    print("\n💡 下一步:")
    print("1. 重启应用程序: python app.py")
    print("2. 在Web端登录并查看任务列表")
    print("3. 尝试创建新的查询任务")
    print("4. 检查任务是否正常显示和执行")

if __name__ == "__main__":
    main()
