```json
{
    "estimated_arrival_time": "2025-08-31",
    "estimated_arrival_port": "Shanghai, CN",
    "dates": [
        {
            "date": "2025-08-31",
            "original_format": "31/08/2025",
            "type": "POD_ETA",
            "location": "Shanghai,  CN",
            "description": "Estimated Time of Arrival",
            "status": "estimated",
            "vessel_info": "MSC UBERTY VIII GO534NFlagLIBERIAIMO9337444Built2008",
            "context": "Empty/Laden/Vessel/VoyageEquipment handling facility name"
        },
        {
            "date": "2025-08-31",
            "original_format": "31.08.2025",
            "type": "Tracking results",
            "location": "",
            "description": "Tracking results provided by <PERSON>C",
            "status": "actual",
            "vessel_info": "",
            "context": ""
        },
        {
            "date": "2025-07-30",
            "original_format": "30/07/2025",
            "type": "Transshipment Loaded",
            "location": "Rodman,  PA",
            "description": "Full Transshipment Loaded",
            "status": "estimated",
            "vessel_info": "MSC UBERTY VIII XA530AFlagLIBERIAIMO9337444Built2008",
            "context": "Panama singapur international terminalSMDGPPIT"
        },
        {
            "date": "2025-07-27",
            "original_format": "27/07/2025",
            "type": "Transshipment Positioned In",
            "location": "Rodman,  PA",
            "description": "Full Transshipment Positioned In",
            "status": "estimated",
            "vessel_info": "Panama singapur international terminalSMDGPPIT",
            "context": "LADEN"
        },
        {
            "date": "2025-07-27",
            "original_format": "27/07/2025",
            "type": "Transshipment Positioned Out",
            "location": "Colon,  PA",
            "description": "Full Transshipment Positioned Out",
            "status": "estimated",
            "vessel_info": "Colon container terminalSMDGCCT",
            "context": "LADEN"
        },
        {
            "date": "2025-07-18",
            "original_format": "18/07/2025",
            "type": "Transshipment Discharged",
            "location": "Colon,  PA",
            "description": "Full Transshipment Discharged",
            "status": "estimated",
            "vessel_info": "MSC JULIANA III PH529AFlagLIBERIAIMO9275036Built2003",
            "context": "Colon container terminalSMDGCCT"
        },
        {
            "date": "2025-07-15",
            "original_format": "15/07/2025",
            "type": "Export Loaded on Vessel",
            "location": "Moin,  CR",
            "description": "Export Loaded on Vessel",
            "status": "estimated",
            "vessel_info": "MSC JULIANA III PH527RFlagLIBERIAIMO9275036Built2003",
            "context": "Apm terminal moinSMDGAPMT"
        },
        {
            "date": "2025-07-11",
            "original_format": "11/07/2025",
            "type": "Export received at CY",
            "location": "Moin,  CR",
            "description": "Export received at CYLADEN",
            "status": "estimated",
            "vessel_info": "Apm terminal moinSMDGAPMT",
            "context": ""
        },
        {
            "date": "2025-07-07",
            "original_format": "07/07/2025",
            "type": "Empty to Shipper",
            "location": "Puerto limon,  CR",
            "description": "Empty to Shipper",
            "status": "estimated",
            "vessel_info": "Medlog costa ricaBICCOPNMRZF",
            "context": "EMPTY"
        },
        {
            "date": "2025-07-13",
            "original_format": "13/07/2025",
            "type": "Price calculation date",
            "location": "",
            "description": "Price calculation date is indicative. Please contact your local MSC office to verify this information.",
            "status": "estimated",
            "vessel_info": "",
            "context": ""
        }
    ]
}
```